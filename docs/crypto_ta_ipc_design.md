# 加密TA的IPC命令请求和回复设计

## 1. 概述

本文档设计了基于IPC通信的加密TA服务接口，模仿OP-TEE的架构，通过单独的加密TA对接加密库来提供GP标准的加密服务。加密TA管理加密操作句柄，并通过TIPC与客户端TA进行通信。

## 2. 整体架构

```
客户端TA  <--TIPC-->  加密TA  <-->  加密库(Tongsuo)
    |                    |
    |                    |
GP加密API            操作句柄管理
```

## 3. IPC命令定义

### 3.1 命令类型枚举

```c
typedef enum {
    CRYPTO_CMD_ALLOCATE_OPERATION = 0x1000,
    CRYPTO_CMD_FREE_OPERATION,
    CRYPTO_CMD_GET_OPERATION_INFO,
    CRYPTO_CMD_GET_OPERATION_INFO_MULTIPLE,
    CRYPTO_CMD_RESET_OPERATION,
    CRYPTO_CMD_SET_OPERATION_KEY,
    CRYPTO_CMD_SET_OPERATION_KEY2,
    CRYPTO_CMD_COPY_OPERATION,
    
    // 对称加密操作
    CRYPTO_CMD_CIPHER_INIT = 0x2000,
    CRYPTO_CMD_CIPHER_UPDATE,
    CRYPTO_CMD_CIPHER_DO_FINAL,
    
    // 非对称加密操作
    CRYPTO_CMD_ASYMMETRIC_ENCRYPT = 0x3000,
    CRYPTO_CMD_ASYMMETRIC_DECRYPT,
    CRYPTO_CMD_ASYMMETRIC_SIGN_DIGEST,
    CRYPTO_CMD_ASYMMETRIC_VERIFY_DIGEST,
    
    // 消息摘要操作
    CRYPTO_CMD_DIGEST_UPDATE = 0x4000,
    CRYPTO_CMD_DIGEST_DO_FINAL,
    
    // MAC操作
    CRYPTO_CMD_MAC_INIT = 0x5000,
    CRYPTO_CMD_MAC_UPDATE,
    CRYPTO_CMD_MAC_FINAL_COMPUTE,
    CRYPTO_CMD_MAC_FINAL_COMPARE,
    
    // 认证加密操作
    CRYPTO_CMD_AE_INIT = 0x6000,
    CRYPTO_CMD_AE_UPDATE_AAD,
    CRYPTO_CMD_AE_UPDATE,
    CRYPTO_CMD_AE_ENCRYPT_FINAL,
    CRYPTO_CMD_AE_DECRYPT_FINAL,
    
    // 密钥派生操作
    CRYPTO_CMD_DERIVE_KEY = 0x7000,
    
    // 随机数生成
    CRYPTO_CMD_GENERATE_RANDOM = 0x8000,
    
    // 注意：会话管理通过IPC连接自动处理，无需显式命令
    
    CRYPTO_CMD_MAX = 0xFFFF
} crypto_cmd_t;

// 响应位标志（参考存储TA的STORAGE_RESP_BIT）
#define CRYPTO_RESP_BIT (1U << 31)

// 配置常量
#define MAX_OPERATIONS_PER_SESSION 32
#define MSG_BUF_MAX_SIZE (64 * 1024)  // 64KB
```

### 3.2 通用IPC消息结构

```c
// IPC消息头（参考存储TA的storage_msg结构）
typedef struct {
    uint32_t cmd;           // 命令类型
    uint32_t op_id;         // 操作标识符
    uint32_t flags;         // 标志位
    uint32_t size;          // 消息总大小
    int32_t result;         // TEE_Result（响应时使用）
    uint32_t __reserved;    // 保留字段，必须为0
    uint8_t payload[0];     // 命令特定的消息格式
} crypto_msg_t;
```

## 4. 具体命令的请求和回复结构

### 4.1 操作句柄管理

#### 4.1.1 TEE_AllocateOperation

**请求结构:**
```c
typedef struct {
    uint32_t algorithm;     // 算法标识符
    uint32_t mode;          // 操作模式
    uint32_t maxKeySize;    // 最大密钥长度
} crypto_allocate_operation_args_t;

// 完整消息格式
typedef struct {
    crypto_msg_t hdr;
    crypto_allocate_operation_args_t args;
} crypto_allocate_operation_req_t;
```

**回复结构:**
```c
typedef struct {
    uint32_t operation_handle;  // 操作句柄ID
    TEE_OperationInfo info;     // 操作信息
} crypto_allocate_operation_resp_args_t;

// 完整响应格式
typedef struct {
    crypto_msg_t hdr;
    crypto_allocate_operation_resp_args_t args;
} crypto_allocate_operation_resp_t;
```

#### 4.1.2 TEE_FreeOperation

**请求结构:**
```c
typedef struct {
    uint32_t operation_handle;  // 要释放的操作句柄ID
} crypto_free_operation_args_t;

typedef struct {
    crypto_msg_t hdr;
    crypto_free_operation_args_t args;
} crypto_free_operation_req_t;
```

**回复结构:**
```c
// 只返回消息头，无额外数据
typedef struct {
    crypto_msg_t hdr;
} crypto_free_operation_resp_t;
```

#### 4.1.3 TEE_GetOperationInfo

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
} crypto_get_operation_info_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    TEE_OperationInfo info;     // 操作信息
} crypto_get_operation_info_resp_t;
```

#### 4.1.4 TEE_GetOperationInfoMultiple

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t max_key_count;     // 最大密钥数量
} crypto_get_operation_info_multiple_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    TEE_OperationInfoMultiple info; // 多密钥操作信息
    // 后跟可变长度的TEE_OperationInfoKey数组
} crypto_get_operation_info_multiple_resp_t;
```

### 4.2 密钥设置

#### 4.2.1 TEE_SetOperationKey

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t key_handle;        // 密钥句柄ID
} crypto_set_operation_key_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_set_operation_key_resp_t;
```

#### 4.2.2 TEE_SetOperationKey2

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t key1_handle;       // 第一个密钥句柄ID
    uint32_t key2_handle;       // 第二个密钥句柄ID
} crypto_set_operation_key2_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_set_operation_key2_resp_t;
```

### 4.3 对称加密操作

#### 4.3.1 TEE_CipherInit

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t iv_len;            // IV长度
    // 后跟IV数据
} crypto_cipher_init_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_cipher_init_resp_t;
```

#### 4.3.2 TEE_CipherUpdate

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 源数据长度
    uint32_t dest_len;          // 目标缓冲区长度
    // 后跟源数据
} crypto_cipher_update_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    // 后跟加密/解密数据
} crypto_cipher_update_resp_t;
```

#### 4.3.3 TEE_CipherDoFinal

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 源数据长度
    uint32_t dest_len;          // 目标缓冲区长度
    // 后跟源数据
} crypto_cipher_do_final_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    // 后跟最终加密/解密数据
} crypto_cipher_do_final_resp_t;
```

### 4.4 非对称加密操作

#### 4.4.1 TEE_AsymmetricEncrypt

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 源数据长度
    uint32_t dest_len;          // 目标缓冲区长度
    // 后跟源数据
} crypto_asymmetric_encrypt_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    // 后跟加密数据
} crypto_asymmetric_encrypt_resp_t;
```

#### 4.4.2 TEE_AsymmetricDecrypt

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 源数据长度
    uint32_t dest_len;          // 目标缓冲区长度
    // 后跟源数据
} crypto_asymmetric_decrypt_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    // 后跟解密数据
} crypto_asymmetric_decrypt_resp_t;
```

### 4.5 数字签名操作

#### 4.5.1 TEE_AsymmetricSignDigest

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t digest_len;        // 摘要长度
    uint32_t signature_len;     // 签名缓冲区长度
    // 后跟摘要数据
} crypto_asymmetric_sign_digest_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t signature_len;     // 实际签名长度
    // 后跟签名数据
} crypto_asymmetric_sign_digest_resp_t;
```

#### 4.5.2 TEE_AsymmetricVerifyDigest

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t digest_len;        // 摘要长度
    uint32_t signature_len;     // 签名长度
    // 后跟摘要数据和签名数据
} crypto_asymmetric_verify_digest_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据，结果在header.result中
} crypto_asymmetric_verify_digest_resp_t;
```

## 5. 会话管理（参考存储TA模式）

### 5.1 会话自动管理

加密TA采用与存储TA相同的会话管理模式：

1. **自动会话创建**: 当客户端连接到加密TA端口时，会话自动创建
2. **会话与IPC通道绑定**: 每个IPC通道对应一个加密会话
3. **自动会话销毁**: 当IPC连接断开时，会话及其所有资源自动清理

```c
//加密操作链表的单个节点，包含一个会话的所有加密句柄
struct encryption_client_session {
    struct list_node op_list; //操作句柄列表（存储TA方式）

    struct ipc_channel_context context; //上下文
};

// 会话创建（在IPC连接时自动调用）
static struct ipc_channel_context* crypto_connect(
        struct ipc_port_context* parent_ctx,
        const uuid_t* peer_uuid,
        handle_t chan_handle) {
    struct encryption_client_session* session;

    session = calloc(1, sizeof(*session));
    if (session == NULL) {
        TLOGE("out of memory allocating crypto session\n");
        return NULL;
    }

    // 初始化操作句柄列表（存储TA方式）
    list_initialize(&session->op_list);

    // 初始化IPC通道操作
    crypto_channel_ops_init(&session->context.ops);
    return &session->context;
}

// 会话断开（在IPC断开时自动调用）
static void crypto_disconnect(struct ipc_channel_context* context) {
    struct encryption_client_session* session;

    session = containerof(context, struct encryption_client_session, context);

    // 清理所有操作句柄
    crypto_session_close_all_operations(session);

    // 释放会话内存
    free(session);
}
```

## 6. 错误处理（参考存储TA模式）

### 6.1 加密TA错误码定义

```c
// 加密TA错误码（参考storage_err）
enum crypto_err {
    CRYPTO_NO_ERROR = 0,
    CRYPTO_ERR_GENERIC = 1,
    CRYPTO_ERR_NOT_VALID = 2,
    CRYPTO_ERR_NOT_FOUND = 3,
    CRYPTO_ERR_EXIST = 4,
    CRYPTO_ERR_ACCESS = 5,
    CRYPTO_ERR_OUT_OF_MEMORY = 6,
    CRYPTO_ERR_NOT_IMPLEMENTED = 7,
    CRYPTO_ERR_NOT_SUPPORTED = 8,
    CRYPTO_ERR_BAD_PARAMETERS = 9,
    CRYPTO_ERR_BAD_STATE = 10,
    CRYPTO_ERR_SHORT_BUFFER = 11,
    CRYPTO_ERR_COMMUNICATION = 12,
    CRYPTO_ERR_SECURITY = 13,
    CRYPTO_ERR_BUSY = 14,
};

// TEE_Result到crypto_err的转换
static enum crypto_err crypto_translate_error(TEE_Result tee_res)
{
    switch (tee_res) {
    case TEE_SUCCESS:
        return CRYPTO_NO_ERROR;
    case TEE_ERROR_OUT_OF_MEMORY:
        return CRYPTO_ERR_OUT_OF_MEMORY;
    case TEE_ERROR_BAD_PARAMETERS:
        return CRYPTO_ERR_BAD_PARAMETERS;
    case TEE_ERROR_BAD_STATE:
        return CRYPTO_ERR_BAD_STATE;
    case TEE_ERROR_ITEM_NOT_FOUND:
        return CRYPTO_ERR_NOT_FOUND;
    case TEE_ERROR_NOT_IMPLEMENTED:
        return CRYPTO_ERR_NOT_IMPLEMENTED;
    case TEE_ERROR_NOT_SUPPORTED:
        return CRYPTO_ERR_NOT_SUPPORTED;
    case TEE_ERROR_SHORT_BUFFER:
        return CRYPTO_ERR_SHORT_BUFFER;
    case TEE_ERROR_COMMUNICATION:
        return CRYPTO_ERR_COMMUNICATION;
    case TEE_ERROR_SECURITY:
        return CRYPTO_ERR_SECURITY;
    case TEE_ERROR_BUSY:
        return CRYPTO_ERR_BUSY;
    case TEE_ERROR_ACCESS_DENIED:
        return CRYPTO_ERR_ACCESS;
    default:
        return CRYPTO_ERR_GENERIC;
    }
}
```

### 6.2 响应发送函数

```c
// 发送响应（参考存储TA的send_response）
static int send_crypto_response(struct encryption_client_session* session,
                               enum crypto_err result,
                               crypto_msg_t* msg,
                               void* out_data,
                               size_t out_size)
{
    size_t resp_buf_count = 1;
    if (result == CRYPTO_NO_ERROR && out_data != NULL && out_size != 0) {
        ++resp_buf_count;
    }

    struct iovec resp_bufs[2];

    // 设置响应头
    msg->cmd |= CRYPTO_RESP_BIT;
    msg->flags = 0;
    msg->size = sizeof(crypto_msg_t) + out_size;
    msg->result = result;

    resp_bufs[0].iov_base = msg;
    resp_bufs[0].iov_len = sizeof(crypto_msg_t);

    if (resp_buf_count > 1) {
        resp_bufs[1].iov_base = out_data;
        resp_bufs[1].iov_len = out_size;
    }

    return sync_ipc_send_msg(session->context.common.handle,
                            resp_bufs, resp_buf_count, NULL, 0);
}
```

### 4.6 消息摘要操作

#### 4.6.1 TEE_DigestUpdate

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t chunk_len;         // 数据块长度
    // 后跟数据块
} crypto_digest_update_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_digest_update_resp_t;
```

#### 4.6.2 TEE_DigestDoFinal

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t chunk_len;         // 最后数据块长度
    uint32_t hash_len;          // 哈希缓冲区长度
    // 后跟最后数据块
} crypto_digest_do_final_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t hash_len;          // 实际哈希长度
    // 后跟哈希值
} crypto_digest_do_final_resp_t;
```

### 4.7 MAC操作

#### 4.7.1 TEE_MACInit

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t iv_len;            // IV长度（如果需要）
    // 后跟IV数据
} crypto_mac_init_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_mac_init_resp_t;
```

#### 4.7.2 TEE_MACUpdate

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t chunk_len;         // 数据块长度
    // 后跟数据块
} crypto_mac_update_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_mac_update_resp_t;
```

#### 4.7.3 TEE_MACFinalCompute

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t message_len;       // 最后消息块长度
    uint32_t mac_len;           // MAC缓冲区长度
    // 后跟最后消息块
} crypto_mac_final_compute_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t mac_len;           // 实际MAC长度
    // 后跟MAC值
} crypto_mac_final_compute_resp_t;
```

#### 4.7.4 TEE_MACFinalCompare

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t message_len;       // 最后消息块长度
    uint32_t mac_len;           // MAC长度
    // 后跟最后消息块和MAC值
} crypto_mac_final_compare_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据，比较结果在header.result中
} crypto_mac_final_compare_resp_t;
```

### 4.8 认证加密操作

#### 4.8.1 TEE_AEInit

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t nonce_len;         // Nonce长度
    uint32_t tag_len;           // 标签长度
    uint32_t aad_len;           // AAD总长度
    uint32_t payload_len;       // 载荷总长度
    // 后跟Nonce数据
} crypto_ae_init_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_ae_init_resp_t;
```

#### 4.8.2 TEE_AEUpdateAAD

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t aad_len;           // AAD数据长度
    // 后跟AAD数据
} crypto_ae_update_aad_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_ae_update_aad_resp_t;
```

#### 4.8.3 TEE_AEUpdate

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 源数据长度
    uint32_t dest_len;          // 目标缓冲区长度
    // 后跟源数据
} crypto_ae_update_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    // 后跟处理后的数据
} crypto_ae_update_resp_t;
```

#### 4.8.4 TEE_AEEncryptFinal

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 最后数据块长度
    uint32_t dest_len;          // 目标缓冲区长度
    uint32_t tag_len;           // 标签缓冲区长度
    // 后跟最后数据块
} crypto_ae_encrypt_final_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    uint32_t tag_len;           // 实际标签长度
    // 后跟最后加密数据和标签
} crypto_ae_encrypt_final_resp_t;
```

#### 4.8.5 TEE_AEDecryptFinal

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t src_len;           // 最后数据块长度
    uint32_t dest_len;          // 目标缓冲区长度
    uint32_t tag_len;           // 标签长度
    // 后跟最后数据块和标签
} crypto_ae_decrypt_final_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t dest_len;          // 实际输出长度
    // 后跟最后解密数据
} crypto_ae_decrypt_final_resp_t;
```

### 4.9 密钥派生操作

#### 4.9.1 TEE_DeriveKey

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t params_len;        // 参数长度
    uint32_t derived_key_handle; // 派生密钥句柄ID
    // 后跟派生参数
} crypto_derive_key_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    // 无额外数据
} crypto_derive_key_resp_t;
```

### 4.10 随机数生成

#### 4.10.1 TEE_GenerateRandom

**请求结构:**
```c
typedef struct {
    crypto_ipc_header_t header;
    uint32_t random_len;        // 请求的随机数长度
} crypto_generate_random_req_t;
```

**回复结构:**
```c
typedef struct {
    crypto_ipc_response_t header;
    uint32_t random_len;        // 实际随机数长度
    // 后跟随机数据
} crypto_generate_random_resp_t;
```

## 7. 数据传输优化

### 7.1 大数据处理

对于大数据量的加密操作，采用分块传输机制：

```c
// 分块传输标志
#define CRYPTO_FLAG_MORE_DATA    0x00000001  // 还有更多数据
#define CRYPTO_FLAG_LAST_CHUNK   0x00000002  // 最后一块数据
#define CRYPTO_FLAG_FIRST_CHUNK  0x00000004  // 第一块数据

// 分块传输请求
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t chunk_id;          // 块ID
    uint32_t total_size;        // 总数据大小
    uint32_t chunk_size;        // 当前块大小
    uint32_t chunk_flags;       // 块标志
    // 后跟数据块
} crypto_chunk_req_t;
```

### 7.2 共享内存机制

对于超大数据，可以使用共享内存：

```c
// 共享内存请求
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;  // 操作句柄ID
    uint32_t shm_id;            // 共享内存ID
    uint32_t shm_offset;        // 偏移量
    uint32_t data_size;         // 数据大小
} crypto_shm_req_t;
```

## 8. 加密TA内部架构

### 8.1 核心组件

```c
// 加密TA主要组件（简化版，参考存储TA）
typedef struct {
    struct list_node channels;                    // IPC通道列表（存储TA方式）
    bool initialized;                             // 初始化标志
} crypto_ta_context_t;

//加密ta管理的操作句柄
struct __TEE_OperationHandle {
    struct list_node node;  /* 链表节点（存储TA方式） */
    TEE_OperationInfo info;
    TEE_ObjectHandle key1;
    TEE_ObjectHandle key2;
    uint32_t operationState;/* Operation state : INITIAL or ACTIVE */
     uint8_t *buffer;
    bool buffer_two_blocks; /* True if two blocks need to be buffered */
    size_t block_size;  /* Block size of cipher */
    size_t buffer_offs; /* Offset in buffer */
    uint32_t state;     /* Handle to state in TEE Core */
};

//单密钥加密操作信息
typedef struct {
    uint32_t algorithm;
    uint32_t operationClass;
    uint32_t mode;
    uint32_t digestLength;
    uint32_t maxKeySize;
    uint32_t keySize;
    uint32_t requiredKeyUsage;
    uint32_t handleState;
} TEE_OperationInfo;

//加密模式
typedef uint32_t TEE_OperationMode;

//多密钥加密操作信息
typedef struct {
   uint32_t keySize; //密钥大小，在P192中定义
   uint32_t requiredKeyUsage; //密钥用法，在P192中定义
} TEE_OperationInfoKey;

// 链表操作说明（存储TA方式 - LK链表）
/*
 * 使用LK链表API，主要函数：
 * - list_initialize(list): 初始化链表头
 * - list_add_tail(list, item): 添加节点到链表尾部
 * - list_remove_head_type(list, type, element): 移除并返回头部节点
 * - list_for_every_entry(list, var, type, element): 遍历链表
 * - list_delete(item): 删除指定节点
 */

// 会话信息（已在上面定义，这里不重复）
```

### 8.2 消息处理流程

```c
// 消息处理主函数（参考存储TA的client_handle_msg）
static int crypto_handle_msg(struct ipc_channel_context* context,
                             void* msg_buf, size_t msg_size)
{
    struct encryption_client_session* session;
    crypto_msg_t* msg = (crypto_msg_t*)msg_buf;
    enum crypto_err result = CRYPTO_NO_ERROR;
    void* out_data = NULL;
    size_t out_size = 0;

    // 获取会话上下文
    session = containerof(context, struct encryption_client_session, context);

    // 验证消息大小
    if (msg_size < sizeof(crypto_msg_t)) {
        TLOGE("invalid message size %zu\n", msg_size);
        result = CRYPTO_ERR_NOT_VALID;
        goto err_invalid_msg;
    }

    if (msg->size != msg_size) {
        TLOGE("invalid message size %u != %zu\n", msg->size, msg_size);
        result = CRYPTO_ERR_NOT_VALID;
        goto err_invalid_msg;
    }

    // 分发命令处理
    switch (msg->cmd) {
    case CRYPTO_CMD_ALLOCATE_OPERATION:
        result = handle_allocate_operation(session, msg, &out_data, &out_size);
        break;
    case CRYPTO_CMD_FREE_OPERATION:
        result = handle_free_operation(session, msg, &out_data, &out_size);
        break;
    case CRYPTO_CMD_CIPHER_INIT:
        result = handle_cipher_init(session, msg, &out_data, &out_size);
        break;
    case CRYPTO_CMD_CIPHER_UPDATE:
        result = handle_cipher_update(session, msg, &out_data, &out_size);
        break;
    case CRYPTO_CMD_CIPHER_DO_FINAL:
        result = handle_cipher_do_final(session, msg, &out_data, &out_size);
        break;
    // ... 其他命令处理
    default:
        TLOGE("unsupported command %u\n", msg->cmd);
        result = CRYPTO_ERR_NOT_IMPLEMENTED;
        break;
    }

err_invalid_msg:
    // 发送响应
    return send_crypto_response(session, result, msg, out_data, out_size);
}
```

### 8.3 操作句柄管理

```c
// 分配操作句柄
static enum crypto_err allocate_operation_handle(struct encryption_client_session *session,
                                                uint32_t algorithm, uint32_t mode,
                                                uint32_t maxKeySize, uint32_t *op_id)
{
    struct __TEE_OperationHandle *op_handle;
    TEE_Result res;

    // 分配操作句柄
    op_handle = calloc(1, sizeof(*op_handle));
    if (!op_handle)
        return CRYPTO_ERR_OUT_OF_MEMORY;

    // 初始化操作句柄
    res = TEE_AllocateOperation((TEE_OperationHandle*)op_handle,
                               algorithm, mode, maxKeySize);
    if (res != TEE_SUCCESS) {
        free(op_handle);
        return crypto_translate_error(res);
    }

    // 添加到会话操作列表（存储TA方式）
    list_add_tail(&session->op_list, &op_handle->node);

    *op_id = (uint32_t)(uintptr_t)op_handle;  // 使用指针作为句柄ID
    return CRYPTO_NO_ERROR;
}

// 查找操作句柄（存储TA方式）
static struct __TEE_OperationHandle *find_operation_handle(
        struct encryption_client_session *session,
        uint32_t operation_id)
{
    struct __TEE_OperationHandle *op_handle;

    list_for_every_entry(&session->op_list, op_handle, struct __TEE_OperationHandle, node) {
        if ((uint32_t)(uintptr_t)op_handle == operation_id) {
            return op_handle;
        }
    }

    return NULL;
}
```

### 8.4 会话管理

```c
// 清理会话所有操作（存储TA方式）
static void crypto_session_close_all_operations(struct encryption_client_session *session)
{
    struct __TEE_OperationHandle *op_handle;

    while ((op_handle = list_remove_head_type(&session->op_list,
                                             struct __TEE_OperationHandle, node))) {
        // 清理操作句柄资源
        TEE_FreeOperation((TEE_OperationHandle)op_handle);
        if (op_handle->buffer) {
            free(op_handle->buffer);
        }
        free(op_handle);
    }
}

// 释放单个操作句柄（存储TA方式）
static enum crypto_err free_operation_handle(struct encryption_client_session *session,
                                            uint32_t operation_id)
{
    struct __TEE_OperationHandle *op_handle;

    op_handle = find_operation_handle(session, operation_id);
    if (!op_handle) {
        return CRYPTO_ERR_NOT_FOUND;
    }

    // 从链表中移除
    list_delete(&op_handle->node);

    // 清理操作句柄资源
    TEE_FreeOperation((TEE_OperationHandle)op_handle);
    if (op_handle->buffer) {
        free(op_handle->buffer);
    }
    free(op_handle);

    return CRYPTO_NO_ERROR;
}
```

## 9. 实现注意事项

1. **内存管理**: 加密TA需要管理操作句柄的生命周期，确保在会话关闭时释放所有相关资源。

2. **并发控制**: 需要考虑多个客户端同时访问的情况，实现适当的锁机制。

3. **安全性**: 敏感数据（如密钥材料）在传输过程中需要适当保护。

4. **性能优化**: 对于大数据量的操作，考虑使用共享内存或分块传输。

5. **错误恢复**: 实现适当的错误恢复机制，确保系统稳定性。

6. **资源限制**: 限制每个会话可以创建的操作句柄数量，防止资源耗尽。

7. **超时处理**: 对长时间运行的操作实现超时机制。

8. **审计日志**: 记录关键的加密操作，用于安全审计。

9. **引用计数**: 使用引用计数管理操作句柄，防止在使用过程中被意外释放。

10. **线程安全**: 所有共享数据结构都需要适当的锁保护，确保线程安全。

## 10. 与Tongsuo加密库集成

### 10.1 加密库适配层

```c
// Tongsuo适配层接口
typedef struct {
    // 对称加密
    TEE_Result (*cipher_init)(uint32_t algorithm, uint32_t mode,
                             const void *key, size_t key_len,
                             const void *iv, size_t iv_len, void **ctx);
    TEE_Result (*cipher_update)(void *ctx, const void *src, size_t src_len,
                               void *dest, size_t *dest_len);
    TEE_Result (*cipher_final)(void *ctx, void *dest, size_t *dest_len);
    void (*cipher_cleanup)(void *ctx);

    // 非对称加密
    TEE_Result (*asymm_encrypt)(uint32_t algorithm, const void *key, size_t key_len,
                               const void *src, size_t src_len,
                               void *dest, size_t *dest_len);
    TEE_Result (*asymm_decrypt)(uint32_t algorithm, const void *key, size_t key_len,
                               const void *src, size_t src_len,
                               void *dest, size_t *dest_len);

    // 数字签名
    TEE_Result (*sign_digest)(uint32_t algorithm, const void *key, size_t key_len,
                             const void *digest, size_t digest_len,
                             void *signature, size_t *sig_len);
    TEE_Result (*verify_digest)(uint32_t algorithm, const void *key, size_t key_len,
                               const void *digest, size_t digest_len,
                               const void *signature, size_t sig_len);

    // 消息摘要
    TEE_Result (*digest_init)(uint32_t algorithm, void **ctx);
    TEE_Result (*digest_update)(void *ctx, const void *data, size_t data_len);
    TEE_Result (*digest_final)(void *ctx, void *digest, size_t *digest_len);
    void (*digest_cleanup)(void *ctx);

    // MAC
    TEE_Result (*mac_init)(uint32_t algorithm, const void *key, size_t key_len,
                          const void *iv, size_t iv_len, void **ctx);
    TEE_Result (*mac_update)(void *ctx, const void *data, size_t data_len);
    TEE_Result (*mac_final)(void *ctx, void *mac, size_t *mac_len);
    void (*mac_cleanup)(void *ctx);

    // 认证加密
    TEE_Result (*ae_init)(uint32_t algorithm, const void *key, size_t key_len,
                         const void *nonce, size_t nonce_len,
                         size_t tag_len, size_t aad_len, size_t payload_len,
                         void **ctx);
    TEE_Result (*ae_update_aad)(void *ctx, const void *aad, size_t aad_len);
    TEE_Result (*ae_update)(void *ctx, const void *src, size_t src_len,
                           void *dest, size_t *dest_len);
    TEE_Result (*ae_encrypt_final)(void *ctx, const void *src, size_t src_len,
                                  void *dest, size_t *dest_len,
                                  void *tag, size_t *tag_len);
    TEE_Result (*ae_decrypt_final)(void *ctx, const void *src, size_t src_len,
                                  void *dest, size_t *dest_len,
                                  const void *tag, size_t tag_len);
    void (*ae_cleanup)(void *ctx);

    // 密钥派生
    TEE_Result (*derive_key)(uint32_t algorithm, const void *base_key, size_t base_key_len,
                            const void *params, size_t params_len,
                            void *derived_key, size_t *derived_key_len);

    // 随机数生成
    TEE_Result (*generate_random)(void *buffer, size_t size);

} tongsuo_crypto_ops_t;

// 全局Tongsuo操作接口
extern tongsuo_crypto_ops_t g_tongsuo_ops;
```

### 10.2 算法映射表

```c
// GP算法到Tongsuo算法的映射
typedef struct {
    uint32_t gp_algorithm;      // GP算法标识
    const char *tongsuo_name;   // Tongsuo算法名称
    uint32_t key_size_min;      // 最小密钥长度
    uint32_t key_size_max;      // 最大密钥长度
    uint32_t block_size;        // 块大小
    uint32_t iv_size;           // IV大小
} algorithm_mapping_t;

static const algorithm_mapping_t g_algorithm_map[] = {
    // 对称加密算法
    {TEE_ALG_AES_ECB_NOPAD, "AES-128-ECB", 16, 32, 16, 0},
    {TEE_ALG_AES_CBC_NOPAD, "AES-128-CBC", 16, 32, 16, 16},
    {TEE_ALG_AES_CTR, "AES-128-CTR", 16, 32, 16, 16},
    {TEE_ALG_AES_GCM, "AES-128-GCM", 16, 32, 16, 12},
    {TEE_ALG_DES_ECB_NOPAD, "DES-ECB", 8, 8, 8, 0},
    {TEE_ALG_DES_CBC_NOPAD, "DES-CBC", 8, 8, 8, 8},
    {TEE_ALG_DES3_ECB_NOPAD, "DES-EDE3-ECB", 24, 24, 8, 0},
    {TEE_ALG_DES3_CBC_NOPAD, "DES-EDE3-CBC", 24, 24, 8, 8},

    // 国密算法
    {TEE_ALG_SM4_ECB_NOPAD, "SM4-ECB", 16, 16, 16, 0},
    {TEE_ALG_SM4_CBC_NOPAD, "SM4-CBC", 16, 16, 16, 16},
    {TEE_ALG_SM4_CTR, "SM4-CTR", 16, 16, 16, 16},
    {TEE_ALG_SM4_GCM, "SM4-GCM", 16, 16, 16, 12},

    // 非对称加密算法
    {TEE_ALG_RSA_NOPAD, "RSA", 128, 512, 0, 0},
    {TEE_ALG_RSAES_PKCS1_V1_5, "RSA-PKCS1", 128, 512, 0, 0},
    {TEE_ALG_RSAES_PKCS1_OAEP_MGF1_SHA1, "RSA-OAEP", 128, 512, 0, 0},
    {TEE_ALG_SM2_PKE, "SM2", 32, 32, 0, 0},

    // 数字签名算法
    {TEE_ALG_RSASSA_PKCS1_V1_5_SHA1, "RSA-SHA1", 128, 512, 0, 0},
    {TEE_ALG_RSASSA_PKCS1_V1_5_SHA256, "RSA-SHA256", 128, 512, 0, 0},
    {TEE_ALG_RSASSA_PKCS1_PSS_MGF1_SHA1, "RSA-PSS-SHA1", 128, 512, 0, 0},
    {TEE_ALG_RSASSA_PKCS1_PSS_MGF1_SHA256, "RSA-PSS-SHA256", 128, 512, 0, 0},
    {TEE_ALG_ECDSA_P192, "ECDSA-P192", 24, 24, 0, 0},
    {TEE_ALG_ECDSA_P224, "ECDSA-P224", 28, 28, 0, 0},
    {TEE_ALG_ECDSA_P256, "ECDSA-P256", 32, 32, 0, 0},
    {TEE_ALG_ECDSA_P384, "ECDSA-P384", 48, 48, 0, 0},
    {TEE_ALG_ECDSA_P521, "ECDSA-P521", 66, 66, 0, 0},
    {TEE_ALG_SM2_DSA_SM3, "SM2-SM3", 32, 32, 0, 0},

    // 消息摘要算法
    {TEE_ALG_MD5, "MD5", 0, 0, 0, 0},
    {TEE_ALG_SHA1, "SHA1", 0, 0, 0, 0},
    {TEE_ALG_SHA224, "SHA224", 0, 0, 0, 0},
    {TEE_ALG_SHA256, "SHA256", 0, 0, 0, 0},
    {TEE_ALG_SHA384, "SHA384", 0, 0, 0, 0},
    {TEE_ALG_SHA512, "SHA512", 0, 0, 0, 0},
    {TEE_ALG_SM3, "SM3", 0, 0, 0, 0},

    // MAC算法
    {TEE_ALG_HMAC_MD5, "HMAC-MD5", 16, 64, 0, 0},
    {TEE_ALG_HMAC_SHA1, "HMAC-SHA1", 20, 64, 0, 0},
    {TEE_ALG_HMAC_SHA224, "HMAC-SHA224", 28, 64, 0, 0},
    {TEE_ALG_HMAC_SHA256, "HMAC-SHA256", 32, 64, 0, 0},
    {TEE_ALG_HMAC_SHA384, "HMAC-SHA384", 48, 128, 0, 0},
    {TEE_ALG_HMAC_SHA512, "HMAC-SHA512", 64, 128, 0, 0},
    {TEE_ALG_HMAC_SM3, "HMAC-SM3", 32, 64, 0, 0},
    {TEE_ALG_AES_CMAC, "AES-CMAC", 16, 32, 0, 0},

    // 密钥派生算法
    {TEE_ALG_PBKDF2_HMAC_SHA1_DERIVE_KEY, "PBKDF2-HMAC-SHA1", 0, 0, 0, 0},
    {TEE_ALG_PBKDF2_HMAC_SHA256_DERIVE_KEY, "PBKDF2-HMAC-SHA256", 0, 0, 0, 0},
    {TEE_ALG_HKDF_SHA256_DERIVE_KEY, "HKDF-SHA256", 0, 0, 0, 0},

    {0, NULL, 0, 0, 0, 0}  // 结束标记
};

// 查找算法映射
const algorithm_mapping_t *find_algorithm_mapping(uint32_t gp_algorithm)
{
    const algorithm_mapping_t *mapping = g_algorithm_map;

    while (mapping->tongsuo_name) {
        if (mapping->gp_algorithm == gp_algorithm)
            return mapping;
        mapping++;
    }

    return NULL;
}
```

### 10.3 Tongsuo初始化

```c
// Tongsuo库初始化
TEE_Result tongsuo_crypto_init(void)
{
    // 初始化Tongsuo库
    TONGSUO_library_init();
    TONGSUO_load_error_strings();
    TONGSUO_add_all_algorithms();
    TONGSUO_add_all_ciphers();
    TONGSUO_add_all_digests();

    // 初始化随机数生成器
    if (TONGSUO_RAND_status() != 1) {
        EMSG("Tongsuo random number generator not properly seeded");
        return TEE_ERROR_SECURITY;
    }

    // 设置操作接口
    g_tongsuo_ops.cipher_init = tongsuo_cipher_init;
    g_tongsuo_ops.cipher_update = tongsuo_cipher_update;
    g_tongsuo_ops.cipher_final = tongsuo_cipher_final;
    g_tongsuo_ops.cipher_cleanup = tongsuo_cipher_cleanup;

    g_tongsuo_ops.asymm_encrypt = tongsuo_asymm_encrypt;
    g_tongsuo_ops.asymm_decrypt = tongsuo_asymm_decrypt;

    g_tongsuo_ops.sign_digest = tongsuo_sign_digest;
    g_tongsuo_ops.verify_digest = tongsuo_verify_digest;

    g_tongsuo_ops.digest_init = tongsuo_digest_init;
    g_tongsuo_ops.digest_update = tongsuo_digest_update;
    g_tongsuo_ops.digest_final = tongsuo_digest_final;
    g_tongsuo_ops.digest_cleanup = tongsuo_digest_cleanup;

    g_tongsuo_ops.mac_init = tongsuo_mac_init;
    g_tongsuo_ops.mac_update = tongsuo_mac_update;
    g_tongsuo_ops.mac_final = tongsuo_mac_final;
    g_tongsuo_ops.mac_cleanup = tongsuo_mac_cleanup;

    g_tongsuo_ops.ae_init = tongsuo_ae_init;
    g_tongsuo_ops.ae_update_aad = tongsuo_ae_update_aad;
    g_tongsuo_ops.ae_update = tongsuo_ae_update;
    g_tongsuo_ops.ae_encrypt_final = tongsuo_ae_encrypt_final;
    g_tongsuo_ops.ae_decrypt_final = tongsuo_ae_decrypt_final;
    g_tongsuo_ops.ae_cleanup = tongsuo_ae_cleanup;

    g_tongsuo_ops.derive_key = tongsuo_derive_key;
    g_tongsuo_ops.generate_random = tongsuo_generate_random;

    IMSG("Tongsuo crypto library initialized successfully");
    return TEE_SUCCESS;
}

// Tongsuo库清理
void tongsuo_crypto_cleanup(void)
{
    TONGSUO_cleanup();
    IMSG("Tongsuo crypto library cleaned up");
}
```

## 11. 性能优化策略

### 11.1 缓存机制

```c
// 操作上下文缓存
typedef struct {
    uint32_t algorithm;
    uint32_t mode;
    void *cached_ctx;
    uint64_t last_used;
    bool in_use;
} crypto_ctx_cache_t;

#define MAX_CACHED_CONTEXTS 32
static crypto_ctx_cache_t g_ctx_cache[MAX_CACHED_CONTEXTS];
static pthread_mutex_t g_cache_lock = PTHREAD_MUTEX_INITIALIZER;

// 获取缓存的上下文
void *get_cached_context(uint32_t algorithm, uint32_t mode)
{
    crypto_ctx_cache_t *cache_entry = NULL;
    void *ctx = NULL;

    pthread_mutex_lock(&g_cache_lock);
    for (int i = 0; i < MAX_CACHED_CONTEXTS; i++) {
        if (!g_ctx_cache[i].in_use &&
            g_ctx_cache[i].algorithm == algorithm &&
            g_ctx_cache[i].mode == mode &&
            g_ctx_cache[i].cached_ctx != NULL) {
            cache_entry = &g_ctx_cache[i];
            ctx = cache_entry->cached_ctx;
            cache_entry->in_use = true;
            cache_entry->last_used = get_current_time();
            break;
        }
    }
    pthread_mutex_unlock(&g_cache_lock);

    return ctx;
}

// 归还上下文到缓存
void return_context_to_cache(void *ctx, uint32_t algorithm, uint32_t mode)
{
    crypto_ctx_cache_t *cache_entry = NULL;

    pthread_mutex_lock(&g_cache_lock);
    for (int i = 0; i < MAX_CACHED_CONTEXTS; i++) {
        if (g_ctx_cache[i].cached_ctx == ctx) {
            cache_entry = &g_ctx_cache[i];
            cache_entry->in_use = false;
            cache_entry->last_used = get_current_time();
            break;
        }
    }
    pthread_mutex_unlock(&g_cache_lock);
}
```

### 11.2 内存池管理

```c
// 内存池定义
typedef struct {
    void *pool_base;
    size_t pool_size;
    size_t block_size;
    uint32_t total_blocks;
    uint32_t free_blocks;
    uint8_t *free_bitmap;
    pthread_mutex_t pool_lock;
} memory_pool_t;

// 不同大小的内存池
static memory_pool_t g_small_pool;   // 64字节块
static memory_pool_t g_medium_pool;  // 1KB块
static memory_pool_t g_large_pool;   // 16KB块

// 初始化内存池
TEE_Result init_memory_pools(void)
{
    TEE_Result res;

    // 初始化小块内存池
    res = init_memory_pool(&g_small_pool, 64, 1024);
    if (res != TEE_SUCCESS)
        return res;

    // 初始化中块内存池
    res = init_memory_pool(&g_medium_pool, 1024, 256);
    if (res != TEE_SUCCESS) {
        cleanup_memory_pool(&g_small_pool);
        return res;
    }

    // 初始化大块内存池
    res = init_memory_pool(&g_large_pool, 16384, 64);
    if (res != TEE_SUCCESS) {
        cleanup_memory_pool(&g_small_pool);
        cleanup_memory_pool(&g_medium_pool);
        return res;
    }

    return TEE_SUCCESS;
}

// 从内存池分配内存
void *pool_alloc(size_t size)
{
    if (size <= 64)
        return alloc_from_pool(&g_small_pool);
    else if (size <= 1024)
        return alloc_from_pool(&g_medium_pool);
    else if (size <= 16384)
        return alloc_from_pool(&g_large_pool);
    else
        return TEE_Malloc(size, 0);  // 使用系统分配器
}

// 释放内存到内存池
void pool_free(void *ptr, size_t size)
{
    if (size <= 64)
        free_to_pool(&g_small_pool, ptr);
    else if (size <= 1024)
        free_to_pool(&g_medium_pool, ptr);
    else if (size <= 16384)
        free_to_pool(&g_large_pool, ptr);
    else
        TEE_Free(ptr);
}
```

### 11.3 批量操作优化

```c
// 批量加密请求
typedef struct {
    crypto_ipc_header_t header;
    uint32_t operation_handle;
    uint32_t batch_count;       // 批量操作数量
    // 后跟batch_count个crypto_batch_item_t
} crypto_batch_req_t;

typedef struct {
    uint32_t src_offset;        // 源数据偏移
    uint32_t src_len;           // 源数据长度
    uint32_t dest_offset;       // 目标数据偏移
    uint32_t dest_len;          // 目标缓冲区长度
} crypto_batch_item_t;

// 批量处理函数
TEE_Result handle_batch_cipher_update(struct encryption_client_session *session,
                                     crypto_batch_req_t *req, size_t req_size,
                                     void **resp_data, size_t *resp_size)
{
    TEE_OperationHandle_t *op_handle;
    crypto_batch_item_t *items;
    uint8_t *src_data, *dest_data;
    TEE_Result res = TEE_SUCCESS;

    // 查找操作句柄
    op_handle = find_operation_handle(session, req->operation_handle);
    if (!op_handle)
        return TEE_ERROR_BAD_PARAMETERS;

    // 获取批量项目
    items = (crypto_batch_item_t *)(req + 1);
    src_data = (uint8_t *)(items + req->batch_count);

    // 分配响应缓冲区
    size_t total_dest_size = 0;
    for (uint32_t i = 0; i < req->batch_count; i++) {
        total_dest_size += items[i].dest_len;
    }

    *resp_data = pool_alloc(sizeof(crypto_ipc_response_t) + total_dest_size);
    if (!*resp_data) {
        release_operation_handle(op_handle);
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    dest_data = (uint8_t *)*resp_data + sizeof(crypto_ipc_response_t);

    // 批量处理
    size_t dest_offset = 0;
    for (uint32_t i = 0; i < req->batch_count && res == TEE_SUCCESS; i++) {
        size_t dest_len = items[i].dest_len;
        res = g_tongsuo_ops.cipher_update(op_handle->state,
                                         src_data + items[i].src_offset,
                                         items[i].src_len,
                                         dest_data + dest_offset,
                                         &dest_len);
        dest_offset += dest_len;
    }

    *resp_size = sizeof(crypto_ipc_response_t) + dest_offset;
    release_operation_handle(op_handle);

    return res;
}
```

## 12. 安全考虑

### 12.1 密钥材料保护

```c
// 安全内存分配
void *secure_alloc(size_t size)
{
    void *ptr = TEE_Malloc(size, TEE_MALLOC_FILL_ZERO);
    if (ptr) {
        // 标记为敏感内存，防止交换到磁盘
        mlock(ptr, size);
    }
    return ptr;
}

// 安全内存释放
void secure_free(void *ptr, size_t size)
{
    if (ptr) {
        // 清零敏感数据
        TEE_MemFill(ptr, 0, size);
        munlock(ptr, size);
        TEE_Free(ptr);
    }
}

// 密钥材料清理
void cleanup_key_material(TEE_OperationHandle_t *op_handle)
{
    if (op_handle->key1) {
        TEE_FreeTransientObject(op_handle->key1);
        op_handle->key1 = TEE_HANDLE_NULL;
    }

    if (op_handle->key2) {
        TEE_FreeTransientObject(op_handle->key2);
        op_handle->key2 = TEE_HANDLE_NULL;
    }

    if (op_handle->buffer) {
        secure_free(op_handle->buffer, op_handle->block_size * 2);
        op_handle->buffer = NULL;
    }
}
```

### 12.2 访问控制

```c
// 客户端权限检查
typedef struct {
    uint32_t client_id;
    uint32_t allowed_algorithms;    // 允许的算法位掩码
    uint32_t max_key_size;          // 最大密钥长度
    uint32_t max_operations;        // 最大操作数
    bool admin_privileges;          // 管理员权限
} client_permissions_t;

// 检查客户端权限
TEE_Result check_client_permissions(uint32_t client_id, uint32_t algorithm,
                                   uint32_t key_size)
{
    client_permissions_t *perms = get_client_permissions(client_id);
    if (!perms)
        return TEE_ERROR_ACCESS_DENIED;

    // 检查算法权限
    if (!(perms->allowed_algorithms & (1 << get_algorithm_class(algorithm))))
        return TEE_ERROR_ACCESS_DENIED;

    // 检查密钥长度限制
    if (key_size > perms->max_key_size)
        return TEE_ERROR_ACCESS_DENIED;

    return TEE_SUCCESS;
}
```

## 13. 总结

本设计文档详细描述了基于IPC通信的加密TA服务架构，**完全参考存储TA的设计模式**，主要特点包括：

### 13.1 核心设计原则

1. **存储TA模式**: 完全采用存储TA的会话管理模式，无需显式会话管理命令
2. **自动会话管理**: 会话通过IPC连接自动创建和销毁，简化了客户端使用
3. **统一消息格式**: 采用类似`storage_msg`的消息结构，保持一致性
4. **资源自动清理**: 连接断开时自动清理所有相关资源

### 13.2 主要功能特性

1. **完整的GP API支持**: 涵盖对称加密、非对称加密、数字签名、消息摘要、MAC、认证加密等
2. **高效的句柄管理**: 每个会话独立管理操作句柄，确保资源隔离
3. **Tongsuo集成**: 通过适配层无缝集成Tongsuo加密库，支持国密算法
4. **性能优化**: 内存池、上下文缓存、批量操作等优化策略
5. **安全保护**: 密钥材料保护、访问控制、安全内存管理

### 13.3 与存储TA的一致性

- **会话结构**: `crypto_client_session` 对应 `storage_client_session`
- **消息格式**: `crypto_msg_t` 对应 `storage_msg`
- **错误处理**: `crypto_err` 对应 `storage_err`
- **IPC模式**: 完全相同的连接、消息处理、断开流程

### 13.4 实现优势

1. **开发效率**: 复用存储TA的成熟模式，减少开发和调试时间
2. **系统一致性**: 与现有存储TA保持一致的架构风格
3. **维护性**: 熟悉存储TA的开发者可以快速理解和维护
4. **稳定性**: 基于已验证的存储TA架构，降低风险

该设计为trusty-tee项目提供了一个完整且一致的加密服务解决方案，既保证了GP标准的兼容性，又充分利用了Tongsuo库的强大功能。通过采用存储TA的成熟模式，确保了系统架构的一致性和可维护性。
```
